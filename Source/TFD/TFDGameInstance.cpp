// TFDGameInstance.cpp
#include "TFDGameInstance.h"
#include "Engine/World.h"
#include "Engine/NetDriver.h"
#include "Engine/Engine.h"

UTFDGameInstance::UTFDGameInstance()
{
	
}

void UTFDGameInstance::Init()
{
	Super::Init();

	UE_LOG(LogTemp, Log, TEXT("[UTFDGameInstance][Init] Game instance initialized"));
}

void UTFDGameInstance::OnStart()
{
	Super::OnStart();
	
	UE_LOG(LogTemp, Log, TEXT("[UTFDGameInstance][OnStart] Game instance started"));
}

void UTFDGameInstance::OnNetworkFailure(UWorld* World, UNetDriver* NetDriver, ENetworkFailure::Type FailureType, const FString& ErrorString)
{
	Super::OnNetworkFailure(World, NetDriver, FailureType, ErrorString);
	
	FString FailureTypeString;
	switch (FailureType)
	{
	case ENetworkFailure::NetDriverAlreadyExists:
		FailureTypeString = TEXT("NetDriverAlreadyExists");
		break;
	case ENetworkFailure::NetDriverCreateFailure:
		FailureTypeString = TEXT("NetDriverCreateFailure");
		break;
	case ENetworkFailure::NetDriverListenFailure:
		FailureTypeString = TEXT("NetDriverListenFailure");
		break;
	case ENetworkFailure::ConnectionLost:
		FailureTypeString = TEXT("ConnectionLost");
		break;
	case ENetworkFailure::ConnectionTimeout:
		FailureTypeString = TEXT("ConnectionTimeout");
		break;
	case ENetworkFailure::FailureReceived:
		FailureTypeString = TEXT("FailureReceived");
		break;
	case ENetworkFailure::OutdatedClient:
		FailureTypeString = TEXT("OutdatedClient");
		break;
	case ENetworkFailure::OutdatedServer:
		FailureTypeString = TEXT("OutdatedServer");
		break;
	case ENetworkFailure::PendingConnectionFailure:
		FailureTypeString = TEXT("PendingConnectionFailure");
		break;
	case ENetworkFailure::NetGuidMismatch:
		FailureTypeString = TEXT("NetGuidMismatch");
		break;
	case ENetworkFailure::NetChecksumMismatch:
		FailureTypeString = TEXT("NetChecksumMismatch");
		break;
	default:
		FailureTypeString = TEXT("Unknown");
		break;
	}
	
	UE_LOG(LogTemp, Error, TEXT("[UTFDGameInstance][OnNetworkFailure] Network failure: %s - %s"), *FailureTypeString, *ErrorString);
	HandleNetworkError(FString::Printf(TEXT("네트워크 오류: %s"), *ErrorString));
}

void UTFDGameInstance::OnTravelFailure(UWorld* World, ETravelFailure::Type FailureType, const FString& ErrorString)
{
	Super::OnTravelFailure(World, FailureType, ErrorString);
	
	FString FailureTypeString;
	switch (FailureType)
	{
	case ETravelFailure::NoLevel:
		FailureTypeString = TEXT("NoLevel");
		break;
	case ETravelFailure::LoadMapFailure:
		FailureTypeString = TEXT("LoadMapFailure");
		break;
	case ETravelFailure::InvalidURL:
		FailureTypeString = TEXT("InvalidURL");
		break;
	case ETravelFailure::PackageMissing:
		FailureTypeString = TEXT("PackageMissing");
		break;
	case ETravelFailure::PackageVersion:
		FailureTypeString = TEXT("PackageVersion");
		break;
	case ETravelFailure::NoDownload:
		FailureTypeString = TEXT("NoDownload");
		break;
	case ETravelFailure::TravelFailure:
		FailureTypeString = TEXT("TravelFailure");
		break;
	case ETravelFailure::CheatCommands:
		FailureTypeString = TEXT("CheatCommands");
		break;
	case ETravelFailure::PendingNetGameCreateFailure:
		FailureTypeString = TEXT("PendingNetGameCreateFailure");
		break;
	case ETravelFailure::CloudSaveFailure:
		FailureTypeString = TEXT("CloudSaveFailure");
		break;
	case ETravelFailure::ServerTravelFailure:
		FailureTypeString = TEXT("ServerTravelFailure");
		break;
	case ETravelFailure::ClientTravelFailure:
		FailureTypeString = TEXT("ClientTravelFailure");
		break;
	default:
		FailureTypeString = TEXT("Unknown");
		break;
	}
	
	UE_LOG(LogTemp, Error, TEXT("[UTFDGameInstance][OnTravelFailure] Travel failure: %s - %s"), *FailureTypeString, *ErrorString);
	HandleTravelError(FString::Printf(TEXT("레벨 이동 오류: %s"), *ErrorString));
}

void UTFDGameInstance::HandleNetworkError(const FString& ErrorMessage)
{
	// 화면에 에러 메시지 표시
	if (GEngine)
	{
		GEngine->AddOnScreenDebugMessage(-1, 10.0f, FColor::Red, ErrorMessage);
	}
	
	UE_LOG(LogTemp, Error, TEXT("[UTFDGameInstance][HandleNetworkError] %s"), *ErrorMessage);
}

void UTFDGameInstance::HandleTravelError(const FString& ErrorMessage)
{
	// 화면에 에러 메시지 표시
	if (GEngine)
	{
		GEngine->AddOnScreenDebugMessage(-1, 10.0f, FColor::Red, ErrorMessage);
	}
	
	UE_LOG(LogTemp, Error, TEXT("[UTFDGameInstance][HandleTravelError] %s"), *ErrorMessage);
}
