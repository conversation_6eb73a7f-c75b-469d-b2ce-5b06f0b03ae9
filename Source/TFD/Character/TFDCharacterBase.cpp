#include "Character/TFDCharacterBase.h"
#include "GameAbilitySystem/Attibute/TFDAttributeSet.h"
#include "Kismet/GameplayStatics.h"
#include "TFDPlayerDataAsset.h"
#include "Components/CapsuleComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "PlayerState/TFDPlayerState.h"
#include "Net/UnrealNetwork.h"

ATFDCharacterBase::ATFDCharacterBase()
{
	PrimaryActorTick.bCanEverTick = true;

	// 네트워크 리플리케이션 설정
	bReplicates = true;
	SetReplicateMovement(true);

	// ASC 생성
	AbilitySystemComponent = CreateDefaultSubobject<UAbilitySystemComponent>(TEXT("AbilitySystemComp"));
	AbilitySystemComponent->SetIsReplicated(true);
	AbilitySystemComponent->SetReplicationMode(EGameplayEffectReplicationMode::Mixed); // or Full

	// AttributeSet 생성
	AttributeSet = CreateDefaultSubobject<UTFDAttributeSet>(TEXT("AttributeSet"));
}

// Called when the game starts or when spawned
void ATFDCharacterBase::BeginPlay()
{
	Super::BeginPlay();
	BaseSetting();
}

void ATFDCharacterBase::OnRep_PlayerState()
{
	Super::OnRep_PlayerState();

	if (APlayerState* PS = GetPlayerState())
	{
		if (AbilitySystemComponent)
		{
			// ASC의 Owner는 PlayerState, Avatar는 이 캐릭터(Pawn)
			AbilitySystemComponent->InitAbilityActorInfo(PS, this);
		}
	}
}

UAbilitySystemComponent* ATFDCharacterBase::GetAbilitySystemComponent() const
{
	return AbilitySystemComponent;
}

void ATFDCharacterBase::NotifyControllerChanged()
{
	Super::NotifyControllerChanged();
}

void ATFDCharacterBase::PossessedBy(AController* NewController)
{
	Super::PossessedBy(NewController);

	// 서버일 때만 실행
	if (HasAuthority())
	{
		if (APlayerState* PS = GetPlayerState())
		{
			if (AbilitySystemComponent)
			{
				AbilitySystemComponent->InitAbilityActorInfo(PS, this);
			}
		}

		SetDAPlayerStat();

	}

}

void ATFDCharacterBase::BaseSetting()
{
	UE_LOG(LogTemp, Warning, TEXT("BaseSetting"));
	// Set size for collision capsule
	GetCapsuleComponent()->InitCapsuleSize(42.f, 96.0f);

	// Configure character movement
	GetCharacterMovement()->bOrientRotationToMovement = true; // Character moves in the direction of input...
	GetCharacterMovement()->RotationRate = FRotator(0.0f, 500.0f, 0.0f); // ...at this rotation rate

	// Note: For faster iteration times these variables, and many more, can be tweaked in the Character Blueprint
	// instead of recompiling to adjust them
	GetCharacterMovement()->JumpZVelocity = 700.f;
	GetCharacterMovement()->AirControl = 0.35f;
	GetCharacterMovement()->MaxWalkSpeed = 500.f;
	GetCharacterMovement()->MinAnalogWalkSpeed = 20.f;
	GetCharacterMovement()->BrakingDecelerationWalking = 2000.f;
	GetCharacterMovement()->BrakingDecelerationFalling = 1500.0f;

	// 애니메이션 리플리케이션을 위한 설정
	if (GetMesh())
	{
		GetMesh()->SetIsReplicated(true);
		// 애니메이션 블루프린트가 네트워크에서 동기화되도록 설정
		GetMesh()->bOnlyOwnerSee = false;
		GetMesh()->bOwnerNoSee = false;

		// 네트워크 업데이트 빈도 설정 (애니메이션 동기화를 위해)
		bReplicates = true;
		SetReplicateMovement(true);
		SetNetUpdateFrequency(60.f);
		SetMinNetUpdateFrequency(30.f);

		UE_LOG(LogTemp, Log, TEXT("Mesh replication settings applied for: %s"), *GetName());
	}
}

void ATFDCharacterBase::SetDAPlayerStat()
{
	if (!HasAuthority()) return;

	if (CharacterData && AttributeSet)
	{
		// AttributeSet의 초기값을 데이터 에셋의 값으로 설정
		AttributeSet->SetHealth(CharacterData->Health);
		AttributeSet->SetMaxHealth(CharacterData->MaxHealth);
		AttributeSet->SetMana(CharacterData->Mana);
		AttributeSet->SetMaxMana(CharacterData->MaxMana);
		AttributeSet->SetSpeed(CharacterData->Speed);

		if (GetCharacterMovement() && AttributeSet)
		{
			GetCharacterMovement()->MaxWalkSpeed = AttributeSet->GetSpeed();

			AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(
				UTFDAttributeSet::GetSpeedAttribute()).AddUObject(this, &ATFDCharacterBase::OnSpeedAttributeChanged);
		}

		//JobDataAsset - Give Ability
		for (const auto& AbilityClass : CharacterData->StartupAbilities)
		{
			if (AbilityClass)
			{
				AbilitySystemComponent->GiveAbility(FGameplayAbilitySpec(AbilityClass, 1, 0, this));
			}
		}

		//JobDataAsset - 팀태그 넘겨주는 코드
		if (AbilitySystemComponent && CharacterData)
		{
			AbilitySystemComponent->AddLooseGameplayTag(CharacterData->TeamTag);
		}
	}
}

void ATFDCharacterBase::OnSpeedAttributeChanged(const FOnAttributeChangeData& Data)
{
	if (UCharacterMovementComponent* MoveComp = GetCharacterMovement())
	{
		MoveComp->MaxWalkSpeed = Data.NewValue;
	}
}

void ATFDCharacterBase::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	// CharacterData를 리플리케이션하여 모든 클라이언트에서 동일한 데이터를 사용하도록 함
	DOREPLIFETIME(ATFDCharacterBase, CharacterData);
}