// TFDGameModeBase_Lobby.cpp
#include "GameMode/TFDGameModeBase_Lobby.h"

#include "GameState/TFDGameStateBase_Lobby.h"
#include "Controller/TFDPlayerController_Title.h"
#include "Constants/TFDGameConstants.h"
#include "GameFramework/PlayerState.h"
#include "Engine/World.h"
#include "Engine/NetDriver.h"

ATFDGameModeBase_Lobby::ATFDGameModeBase_Lobby()
{
	// 게임스테이트 클래스 설정
	GameStateClass = ATFDGameStateBase_Lobby::StaticClass();

	// 네트워크 설정
	bUseSeamlessTravel = false; // 로비에서는 seamless travel 사용하지 않음

	UE_LOG(LogTemp, Log, TEXT("[ATFDGameModeBase_Lobby] Constructor called"));
}

void ATFDGameModeBase_Lobby::InitGame(const FString& MapName, const FString& Options, FString& ErrorMessage)
{
	Super::InitGame(MapName, Options, ErrorMessage);

	UE_LOG(LogTemp, Log, TEXT("[ATFDGameModeBase_Lobby][InitGame] Map: %s, Options: %s"), *MapName, *Options);

	// 네트워크 드라이버 확인
	if (UWorld* World = GetWorld())
	{
		if (UNetDriver* NetDriver = World->GetNetDriver())
		{
			UE_LOG(LogTemp, Log, TEXT("[ATFDGameModeBase_Lobby][InitGame] NetDriver found: %s"), *NetDriver->GetName());
			UE_LOG(LogTemp, Log, TEXT("[ATFDGameModeBase_Lobby][InitGame] NetMode: %d"), (int32)World->GetNetMode());
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("[ATFDGameModeBase_Lobby][InitGame] No NetDriver found!"));
		}
	}
}

void ATFDGameModeBase_Lobby::BeginPlay()
{
	Super::BeginPlay();

	UE_LOG(LogTemp, Log, TEXT("[ATFDGameModeBase_Lobby][BeginPlay] Lobby game mode started"));

	// 네트워크 상태 확인
	if (UWorld* World = GetWorld())
	{
		ENetMode NetMode = World->GetNetMode();
		UE_LOG(LogTemp, Log, TEXT("[ATFDGameModeBase_Lobby][BeginPlay] NetMode: %s"),
			NetMode == NM_Standalone ? TEXT("Standalone") :
			NetMode == NM_DedicatedServer ? TEXT("DedicatedServer") :
			NetMode == NM_ListenServer ? TEXT("ListenServer") :
			NetMode == NM_Client ? TEXT("Client") : TEXT("Unknown"));
	}
}

void ATFDGameModeBase_Lobby::PreLogin(const FString& Options, const FString& Address, const FUniqueNetIdRepl& UniqueId, FString& ErrorMessage)
{
	Super::PreLogin(Options, Address, UniqueId, ErrorMessage);

	UE_LOG(LogTemp, Log, TEXT("[ATFDGameModeBase_Lobby][PreLogin] Player attempting to join from: %s"), *Address);

	// 현재 접속자 수 확인
	ATFDGameStateBase_Lobby* LobbyGS = GetGameState<ATFDGameStateBase_Lobby>();
	if (LobbyGS)
	{
		int32 CurrentCount = LobbyGS->GetConnectedPlayerCount();
		UE_LOG(LogTemp, Log, TEXT("[ATFDGameModeBase_Lobby][PreLogin] Current player count: %d/%d"), CurrentCount, TFDGameConstants::MaxPlayerCount);

		if (CurrentCount >= TFDGameConstants::MaxPlayerCount)
		{
			ErrorMessage = TEXT("Server is full");
			UE_LOG(LogTemp, Warning, TEXT("[ATFDGameModeBase_Lobby][PreLogin] Rejecting player - server full"));
		}
	}
}

void ATFDGameModeBase_Lobby::PostLogin(APlayerController* NewPlayer)
{
	Super::PostLogin(NewPlayer);

	if (!NewPlayer)
	{
		UE_LOG(LogTemp, Error, TEXT("[ATFDGameModeBase_Lobby][PostLogin] NewPlayer is null!"));
		return;
	}

	UE_LOG(LogTemp, Log, TEXT("[ATFDGameModeBase_Lobby][PostLogin] Player joined: %s"),
		NewPlayer->PlayerState ? *NewPlayer->PlayerState->GetPlayerName() : TEXT("Unknown"));

	ATFDGameStateBase_Lobby* LobbyGS = GetGameState<ATFDGameStateBase_Lobby>();
	if (!LobbyGS)
	{
		UE_LOG(LogTemp, Error, TEXT("[ATFDGameModeBase_Lobby][PostLogin] LobbyGameState is null!"));
		return;
	}

	// 접속 인원 체크
	int32 CurrentCount = LobbyGS->GetConnectedPlayerCount();
	UE_LOG(LogTemp, Log, TEXT("[ATFDGameModeBase_Lobby][PostLogin] Current player count: %d/%d"), CurrentCount, TFDGameConstants::MaxPlayerCount);

	//===================================================
	// 접속 허용 처리
	//===================================================
	if (NewPlayer->PlayerState)
	{
		// 이름 지정
		FString PlayerName = FString::Printf(TEXT("Player_%d"), NewPlayer->PlayerState->GetPlayerId());
		NewPlayer->PlayerState->SetPlayerName(PlayerName);
		UE_LOG(LogTemp, Log, TEXT("[ATFDGameModeBase_Lobby][PostLogin] Player name set to: %s"), *PlayerName);

		// 네트워크 상태 로그
		if (UWorld* World = GetWorld())
		{
			ENetMode NetMode = World->GetNetMode();
			UE_LOG(LogTemp, Log, TEXT("[ATFDGameModeBase_Lobby][PostLogin] NetMode: %s, HasAuthority: %s"),
				NetMode == NM_ListenServer ? TEXT("ListenServer") : TEXT("Other"),
				HasAuthority() ? TEXT("Yes") : TEXT("No"));
		}

		// GameState의 델리게이트를 통해 접속자 리스트 갱신 알림
		LobbyGS->OnPlayerListChanged.Broadcast();

		UE_LOG(LogTemp, Log, TEXT("[ATFDGameModeBase_Lobby][PostLogin] Player successfully joined lobby"));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("[ATFDGameModeBase_Lobby][PostLogin] PlayerState is null!"));
	}
}
