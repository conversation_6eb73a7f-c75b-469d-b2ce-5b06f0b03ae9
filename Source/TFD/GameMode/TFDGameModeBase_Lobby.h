// ATFDGameModeBase_Lobby.h
/*
	접속 처리
	PlayerState 이름 설정, GameState에 델리게이트 호출
*/
#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"  // UI 위젯 관련 헤더
#include "GameFramework/GameMode.h"
#include "TFDGameModeBase_Lobby.generated.h"

UCLASS()
class TFD_API ATFDGameModeBase_Lobby : public AGameMode
{
	GENERATED_BODY()

public:
	ATFDGameModeBase_Lobby();

	// 게임모드 초기화
	virtual void InitGame(const FString& MapName, const FString& Options, FString& ErrorMessage) override;
	virtual void BeginPlay() override;

	// 플레이어가 접속했을 때 호출되는 함수 오버라이드
	virtual void PostLogin(APlayerController* NewPlayer) override;

	// 플레이어 접속 거부 처리
	virtual void PreLogin(const FString& Options, const FString& Address, const FUniqueNetIdRepl& UniqueId, FString& ErrorMessage) override;
};
