// TFDGameInstance.h
#pragma once

#include "CoreMinimal.h"
#include "Engine/GameInstance.h"
#include "TFDGameInstance.generated.h"

UCLASS()
class TFD_API UTFDGameInstance : public UGameInstance
{
	GENERATED_BODY()

public:
	UTFDGameInstance();

	// GameInstance 초기화
	virtual void Init() override;
	virtual void OnStart() override;

	// 네트워크 관련 함수들
	virtual void OnNetworkFailure(UWorld* World, UNetDriver* NetDriver, ENetworkFailure::Type FailureType, const FString& ErrorString) override;
	virtual void OnTravelFailure(UWorld* World, ETravelFailure::Type FailureType, const FString& ErrorString) override;

protected:
	// 네트워크 실패 처리
	void HandleNetworkError(const FString& ErrorMessage);
	void HandleTravelError(const FString& ErrorMessage);
};
