{"FileVersion": 3, "Version": 1, "VersionName": "0.0.1", "FriendlyName": "ML Adapter", "Description": "A framework for training and utilizing machine learning agents in games. Creates an RPC interface through which an external process can query game state and control in-game actors. Once trained, agents can be run in-engine via neural networks loaded from ONNX models.", "Category": "AI", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": false, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "MLAdapterTestSuite", "Type": "DeveloperTool", "LoadingPhase": "<PERSON><PERSON>efault"}], "Plugins": [{"Name": "GameplayAbilities", "Enabled": true}, {"Name": "EnhancedInput", "Enabled": true}]}