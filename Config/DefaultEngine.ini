[/Script/EngineSettings.GameMapsSettings]
GameDefaultMap=/Game/Cartoon_City_Free/Maps/Demonstration.Demonstration
EditorStartupMap=/Game/Cartoon_City_Free/Maps/Demonstration.Demonstration
GlobalDefaultGameMode=/Game/BluePrint/GameMode/BP_TFDGameMode.BP_TFDGameMode_C
ServerDefaultMap=/Game/Maps/L_Title.L_Title
GameInstanceClass=/Script/TFD.TFDGameInstance

[/Script/Engine.RendererSettings]
r.Mobile.ShadingPath=0
r.Mobile.AllowDeferredShadingOpenGL=False
r.Mobile.SupportGPUScene=True
r.Mobile.AntiAliasing=1
r.Mobile.FloatPrecisionMode=0
r.Mobile.AllowDitheredLODTransition=False
r.Mobile.VirtualTextures=False
r.DiscardUnusedQuality=False
r.AllowOcclusionQueries=True
r.MinScreenRadiusForLights=0.030000
r.MinScreenRadiusForDepthPrepass=0.030000
r.PrecomputedVisibilityWarning=False
r.TextureStreaming=True
Compat.UseDXT5NormalMaps=False
r.VirtualTextures=False
r.VT.EnableAutoImport=True
r.VirtualTexturedLightmaps=False
r.VT.AnisotropicFiltering=False
bEnableVirtualTextureOpacityMask=True
bEnableVirtualTexturePostProcessing=False
r.VT.TileSize=128
r.VT.TileBorderSize=4
r.vt.FeedbackFactor=16
r.MeshPaintVirtualTexture.TileSize=32
r.MeshPaintVirtualTexture.TileBorderSize=2
r.MeshPaintVirtualTexture.UseCompression=True
r.StaticMesh.DefaultMeshPaintTextureSupport=True
r.MeshPaintVirtualTexture.DefaultTexelsPerVertex=4
r.MeshPaintVirtualTexture.MaxTextureSize=4096
r.vt.rvt.EnableBaseColor=True
r.vt.rvt.EnableBaseColorRoughness=True
r.vt.rvt.EnableBaseColorSpecular=True
r.vt.rvt.EnableMask4=True
r.vt.rvt.EnableWorldHeight=True
r.vt.rvt.EnableDisplacement=True
r.vt.rvt.HighQualityPerPixelHeight=True
WorkingColorSpaceChoice=sRGB
RedChromaticityCoordinate=(X=0.640000,Y=0.330000)
GreenChromaticityCoordinate=(X=0.300000,Y=0.600000)
BlueChromaticityCoordinate=(X=0.150000,Y=0.060000)
WhiteChromaticityCoordinate=(X=0.312700,Y=0.329000)
r.LegacyLuminanceFactors=False
r.ClearCoatNormal=False
r.DynamicGlobalIlluminationMethod=1
r.ReflectionMethod=1
r.ReflectionCaptureResolution=128
r.ReflectionEnvironmentLightmapMixBasedOnRoughness=True
r.Lumen.HardwareRayTracing=True
r.Lumen.HardwareRayTracing.LightingMode=0
r.Lumen.TranslucencyReflections.FrontLayer.EnableForProject=False
r.Lumen.TraceMeshSDFs=0
r.Lumen.ScreenTracingSource=0
r.Lumen.Reflections.HardwareRayTracing.Translucent.Refraction.EnableForProject=True
r.MegaLights.EnableForProject=False
r.RayTracing.Shadows=False
r.Shadow.Virtual.Enable=1
r.RayTracing=True
r.RayTracing.UseTextureLod=False
r.PathTracing=True
r.GenerateMeshDistanceFields=True
r.DistanceFields.DefaultVoxelDensity=0.200000
r.Nanite.ProjectEnabled=True
r.AllowStaticLighting=False
r.NormalMapsForStaticLighting=False
r.ForwardShading=False
r.VertexFoggingForOpaque=True
r.SeparateTranslucency=True
r.TranslucentSortPolicy=0
TranslucentSortAxis=(X=0.000000,Y=-1.000000,Z=0.000000)
r.LocalFogVolume.ApplyOnTranslucent=False
xr.VRS.FoveationLevel=0
xr.VRS.DynamicFoveation=False
r.CustomDepth=1
r.CustomDepthTemporalAAJitter=True
r.PostProcessing.PropagateAlpha=False
r.Deferred.SupportPrimitiveAlphaHoldout=False
r.DefaultFeature.Bloom=True
r.DefaultFeature.AmbientOcclusion=True
r.DefaultFeature.AmbientOcclusionStaticFraction=True
r.DefaultFeature.AutoExposure=False
r.DefaultFeature.AutoExposure.Method=0
r.DefaultFeature.AutoExposure.Bias=1.000000
r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange=True
r.DefaultFeature.LocalExposure.HighlightContrastScale=0.800000
r.DefaultFeature.LocalExposure.ShadowContrastScale=0.800000
r.DefaultFeature.MotionBlur=False
r.DefaultFeature.LensFlare=False
r.TemporalAA.Upsampling=True
r.AntiAliasingMethod=0
r.MSAACount=4
r.DefaultFeature.LightUnits=1
r.DefaultBackBufferPixelFormat=4
r.ScreenPercentage.Default=100.000000
r.ScreenPercentage.Default.Desktop.Mode=1
r.ScreenPercentage.Default.Mobile.Mode=0
r.ScreenPercentage.Default.VR.Mode=0
r.ScreenPercentage.Default.PathTracer.Mode=0
r.Shadow.UnbuiltPreviewInGame=True
r.StencilForLODDither=False
r.EarlyZPass=3
r.EarlyZPassOnlyMaterialMasking=False
r.Shadow.CSMCaching=False
r.DBuffer=True
r.ClearSceneMethod=1
r.VelocityOutputPass=0
r.Velocity.EnableVertexDeformation=2
r.SelectiveBasePassOutputs=False
bDefaultParticleCutouts=False
fx.GPUSimulationTextureSizeX=1024
fx.GPUSimulationTextureSizeY=1024
r.AllowGlobalClipPlane=False
r.GBufferFormat=1
r.MorphTarget.Mode=True
r.MorphTarget.MaxBlendWeight=5.000000
r.SupportSkyAtmosphere=True
r.SupportSkyAtmosphereAffectsHeightFog=True
r.SupportExpFogMatchesVolumetricFog=False
r.SupportLocalFogVolumes=True
r.SupportCloudShadowOnForwardLitTranslucent=False
r.LightFunctionAtlas.Format=0
r.VolumetricFog.LightFunction=True
r.Deferred.UsesLightFunctionAtlas=True
r.SingleLayerWater.UsesLightFunctionAtlas=False
r.Translucent.UsesLightFunctionAtlas=False
r.Translucent.UsesIESProfiles=False
r.Translucent.UsesRectLights=False
r.GPUCrashDebugging=False
vr.InstancedStereo=False
r.MobileHDR=True
vr.MobileMultiView=False
r.Mobile.UseHWsRGBEncoding=False
vr.RoundRobinOcclusion=False
r.MeshStreaming=False
r.HeterogeneousVolumes=True
r.HeterogeneousVolumes.Shadows=False
r.Translucency.HeterogeneousVolumes=False
r.WireframeCullThreshold=5.000000
r.SupportStationarySkylight=True
r.SupportLowQualityLightmaps=True
r.SupportPointLightWholeSceneShadows=True
r.Shadow.TranslucentPerObject.ProjectEnabled=False
r.Water.SingleLayerWater.SupportCloudShadow=False
r.Substrate=False
r.Substrate.OpaqueMaterialRoughRefraction=False
r.Refraction.Blur=True
r.Substrate.Debug.AdvancedVisualizationShaders=False
r.Material.RoughDiffuse=False
r.Material.EnergyConservation=False
r.Material.DefaultAutoMaterialUsage=True
r.OIT.SortedPixels=False
r.HairStrands.LODMode=True
r.SkinCache.CompileShaders=True
r.VRS.Support=True
r.SkinCache.SkipCompilingGPUSkinVF=False
r.SkinCache.DefaultBehavior=1
r.SkinCache.SceneMemoryLimitInMB=128.000000
r.Mobile.EnableStaticAndCSMShadowReceivers=True
r.Mobile.EnableMovableLightCSMShaderCulling=True
r.Mobile.Forward.EnableLocalLights=1
r.Mobile.Forward.EnableClusteredReflections=False
r.Mobile.AllowDistanceFieldShadows=True
r.Mobile.EnableMovableSpotlightsShadow=False
r.GPUSkin.Support16BitBoneIndex=False
r.GPUSkin.Limit2BoneInfluences=False
r.SupportDepthOnlyIndexBuffers=False
r.SupportReversedIndexBuffers=False
r.Mobile.AmbientOcclusion=False
r.Mobile.DBuffer=False
r.GPUSkin.UnlimitedBoneInfluences=False
r.GPUSkin.AlwaysUseDeformerForUnlimitedBoneInfluences=False
r.GPUSkin.UnlimitedBoneInfluencesThreshold=8
DefaultBoneInfluenceLimit=(Default=0,PerPlatform=())
MaxSkinBones=(Default=65536,PerPlatform=(("Mobile", 256)))
r.Mobile.PlanarReflectionMode=0
r.Mobile.ScreenSpaceReflections=False
r.Mobile.SupportsGen4TAA=True
bStreamSkeletalMeshLODs=(Default=False,PerPlatform=())
bDiscardSkeletalMeshOptionalLODs=(Default=False,PerPlatform=())
VisualizeCalibrationColorMaterialPath=/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor
VisualizeCalibrationCustomMaterialPath=None
VisualizeCalibrationGrayscaleMaterialPath=/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale

[/Script/WindowsTargetPlatform.WindowsTargetSettings]
DefaultGraphicsRHI=DefaultGraphicsRHI_DX12
DefaultGraphicsRHI=DefaultGraphicsRHI_DX12
-D3D12TargetedShaderFormats=PCD3D_SM5
+D3D12TargetedShaderFormats=PCD3D_SM6
-D3D11TargetedShaderFormats=PCD3D_SM5
+D3D11TargetedShaderFormats=PCD3D_SM5
Compiler=Default
AudioSampleRate=48000
AudioCallbackBufferFrameSize=1024
AudioNumBuffersToEnqueue=1
AudioMaxChannels=0
AudioNumSourceWorkers=4
SpatializationPlugin=
SourceDataOverridePlugin=
ReverbPlugin=
OcclusionPlugin=
CompressionOverrides=(bOverrideCompressionTimes=False,DurationThreshold=5.000000,MaxNumRandomBranches=0,SoundCueQualityIndex=0)
CacheSizeKB=65536
MaxChunkSizeOverrideKB=0
bResampleForDevice=False
MaxSampleRate=48000.000000
HighSampleRate=32000.000000
MedSampleRate=24000.000000
LowSampleRate=12000.000000
MinSampleRate=8000.000000
CompressionQualityModifier=1.000000
AutoStreamingThreshold=0.000000
SoundCueCookQualityIndex=-1

[/Script/LinuxTargetPlatform.LinuxTargetSettings]
-TargetedRHIs=SF_VULKAN_SM5
+TargetedRHIs=SF_VULKAN_SM6

[/Script/HardwareTargeting.HardwareTargetingSettings]
TargetedHardwareClass=Desktop
AppliedTargetedHardwareClass=Desktop
DefaultGraphicsPerformance=Scalable
AppliedDefaultGraphicsPerformance=Scalable

[/Script/Engine.Engine]
+ActiveGameNameRedirects=(OldGameName="TP_ThirdPerson",NewGameName="/Script/TFD")
+ActiveGameNameRedirects=(OldGameName="/Script/TP_ThirdPerson",NewGameName="/Script/TFD")
+ActiveClassRedirects=(OldClassName="TP_ThirdPersonGameMode",NewClassName="TFDGameMode")
+ActiveClassRedirects=(OldClassName="TP_ThirdPersonCharacter",NewClassName="TFDCharacter")

[/Script/AndroidFileServerEditor.AndroidFileServerRuntimeSettings]
bEnablePlugin=True
bAllowNetworkConnection=True
SecurityToken=3E8315CE48E00E11C908D985216C6DFB
bIncludeInShipping=False
bAllowExternalStartInShipping=False
bCompileAFSProject=False
bUseCompression=False
bLogFiles=False
bReportStats=False
ConnectionType=USBOnly
bUseManualIPAddress=False
ManualIPAddress=

[SystemSettings]
net.AllowPIESeamlessTravel=True

[/Script/Engine.GameNetworkManager]
TotalNetBandwidth=32000
MaxDynamicBandwidth=7000
MinDynamicBandwidth=4000

[/Script/OnlineSubsystemUtils.IpNetDriver]
NetServerMaxTickRate=30
NetClientTicksPerSecond=30
MaxNetTickRate=60
MaxInternetClientRate=10000
MaxClientRate=15000
LanServerMaxTickRate=35
LanClientTicksPerSecond=35
ConnectionTimeout=60.0
InitialConnectTimeout=120.0
AckTimeout=1.0
KeepAliveTime=0.2
MaxPortCountToTry=512
ServerTravelPause=4.0
SpawnPrioritySeconds=1.0
RelevantTimeout=5.0
NetConnectionClassName="IpConnection"

[/Script/Engine.Player]
ConfiguredInternetSpeed=10000
ConfiguredLanSpeed=20000

[Core.System]
Paths=../../../Engine/Content
Paths=%GAMEDIR%Content
Paths=../../../Engine/Plugins/2D/Paper2D/Content
Paths=../../../Engine/Plugins/Developer/AnimationSharing/Content
Paths=../../../Engine/Plugins/Editor/GeometryMode/Content
Paths=../../../Engine/Plugins/Editor/SpeedTreeImporter/Content
Paths=../../../Engine/Plugins/Enterprise/DatasmithContent/Content
Paths=../../../Engine/Plugins/Experimental/ChaosClothEditor/Content
Paths=../../../Engine/Plugins/Experimental/ChaosSolverPlugin/Content
Paths=../../../Engine/Plugins/Experimental/ChaosUserDataPT/Content
Paths=../../../Engine/Plugins/Experimental/CharacterAI/Content
Paths=../../../Engine/Plugins/Experimental/PlanarCutPlugin/Content
Paths=../../../Engine/Plugins/FX/Niagara/Content
Paths=../../../Engine/Plugins/Media/MediaCompositing/Content
Paths=../../../Engine/Plugins/Runtime/Oculus/OculusVR/Content
Paths=../../../Engine/Plugins/Runtime/Steam/SteamVR/Content
Paths=../../../Engine/Plugins/Runtime/Synthesis/Content
Paths=../../../Engine/Plugins/VirtualProduction/Takes/Content
Paths=../../../Engine/Plugins/Experimental/VirtualCamera/Content

